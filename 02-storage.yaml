---
# StorageClass for Azure Disk (Premium SSD) - Node-specific storage
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fluent-bit-node-storage
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
provisioner: disk.csi.azure.com
parameters:
  # Use Premium SSD for better performance
  skuName: Premium_LRS
  # Enable encryption at rest
  encryption: true
  # Set caching mode for better performance
  cachingmode: ReadOnly
  # Set disk type
  kind: Managed
  # Enable node affinity for local storage
  fsType: ext4
reclaimPolicy: Delete  # Changed to Delete for dynamic cleanup
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer  # Ensures PV is created on the same node as the pod

---
# Alternative StorageClass for Azure Files (for shared storage if needed)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fluent-bit-files-storage
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
provisioner: file.csi.azure.com
parameters:
  # Use Premium tier for better performance
  skuName: Premium_LRS
  # Enable encryption
  encryption: true
  # Set protocol
  protocol: nfs
reclaimPolicy: Delete  # Changed to Delete for dynamic cleanup
allowVolumeExpansion: true
volumeBindingMode: Immediate

---
# StorageClass for Local Storage (Alternative for better node affinity)
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fluent-bit-local-storage
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
provisioner: kubernetes.io/no-provisioner
reclaimPolicy: Delete
volumeBindingMode: WaitForFirstConsumer
allowedTopologies:
  - matchLabelExpressions:
      - key: kubernetes.io/hostname
        values: ["*"]

# NOTE: Static PVCs are replaced by dynamic per-node PVCs
# The PVC provisioner controller will create node-specific PVCs automatically
# using the templates defined in the ConfigMap (05-pvc-provisioner.yaml)
#
# Dynamic PVCs will be named as:
# - fluent-bit-buffer-{node-name}
# - fluent-bit-config-{node-name}
# - fluent-bit-db-{node-name}
#
# Each PVC will have node affinity to ensure data locality
