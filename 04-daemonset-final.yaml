---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
    app.kubernetes.io/version: "2.2.0"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/component: logging
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2020"
        prometheus.io/path: /api/v1/metrics/prometheus
    spec:
      serviceAccountName: fluent-bit
      hostNetwork: false
      dnsPolicy: ClusterFirst
      
      # Security context for the pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Node selection and tolerations
      nodeSelector:
        kubernetes.io/os: linux
      
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
          effect: NoSchedule
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
          effect: NoSchedule
        - operator: "Exists"
          effect: "NoExecute"
        - operator: "Exists"
          effect: "NoSchedule"
      
      # Init container to wait for and mount PVCs
      initContainers:
        - name: pvc-waiter
          image: bitnami/kubectl:1.28
          command:
            - /bin/bash
            - -c
            - |
              set -e
              
              NODE_NAME=$(cat /etc/podinfo/nodename)
              echo "Waiting for PVCs for node: $NODE_NAME"
              
              # Wait for all required PVCs to exist and be bound
              for pvc_type in buffer config db; do
                pvc_name="fluent-bit-${pvc_type}-${NODE_NAME}"
                echo "Checking PVC: $pvc_name"
                
                # Wait for PVC to exist (with timeout)
                timeout=300
                elapsed=0
                while ! kubectl get pvc "$pvc_name" -n fluent-bit >/dev/null 2>&1; do
                  if [ $elapsed -ge $timeout ]; then
                    echo "Timeout waiting for PVC $pvc_name to be created"
                    echo "Creating fallback directory structure..."
                    mkdir -p "/shared-storage/${pvc_type}"
                    continue 2
                  fi
                  echo "PVC $pvc_name not found, waiting... (${elapsed}s/${timeout}s)"
                  sleep 5
                  elapsed=$((elapsed + 5))
                done
                
                # Wait for PVC to be bound
                echo "Waiting for PVC $pvc_name to be bound..."
                if ! kubectl wait --for=condition=Bound pvc/"$pvc_name" -n fluent-bit --timeout=300s; then
                  echo "Warning: PVC $pvc_name failed to bind, using fallback storage"
                  mkdir -p "/shared-storage/${pvc_type}"
                else
                  echo "PVC $pvc_name is bound and ready"
                fi
              done
              
              echo "PVC initialization completed for node: $NODE_NAME"
          
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
          
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          
          volumeMounts:
            - name: podinfo
              mountPath: /etc/podinfo
              readOnly: true
            - name: shared-storage
              mountPath: /shared-storage
            - name: tmp
              mountPath: /tmp
        
        # Set up permissions
        - name: init-permissions
          image: busybox:1.35
          command:
            - /bin/sh
            - -c
            - |
              echo "Setting up directory permissions..."
              
              # Create directory structure
              mkdir -p /fluent-bit/db /fluent-bit/buffer /fluent-bit/config-backup
              mkdir -p /shared-storage/db /shared-storage/buffer /shared-storage/config
              
              # Set permissions
              chown -R 1000:1000 /fluent-bit /shared-storage
              chmod -R 755 /fluent-bit /shared-storage
              
              echo "Permissions set successfully"
          
          securityContext:
            runAsUser: 0
            runAsGroup: 0
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
              add:
                - CHOWN
                - FOWNER
          
          volumeMounts:
            - name: fluent-bit-storage
              mountPath: /fluent-bit
            - name: shared-storage
              mountPath: /shared-storage
      
      containers:
        # Storage manager sidecar
        - name: storage-manager
          image: bitnami/kubectl:1.28
          command:
            - /bin/bash
            - -c
            - |
              set -e
              
              NODE_NAME=$(cat /etc/podinfo/nodename)
              echo "Starting storage manager for node: $NODE_NAME"
              
              # Function to mount PVC if available
              mount_pvc_if_available() {
                local pvc_type=$1
                local pvc_name="fluent-bit-${pvc_type}-${NODE_NAME}"
                local mount_point="/fluent-bit/${pvc_type}"
                local fallback_point="/shared-storage/${pvc_type}"
                
                if kubectl get pvc "$pvc_name" -n fluent-bit >/dev/null 2>&1; then
                  echo "PVC $pvc_name is available"
                  # PVC exists, data will be mounted via volume
                  if [ -d "$mount_point" ] && [ "$(ls -A $mount_point 2>/dev/null)" ]; then
                    echo "PVC $pvc_name is properly mounted at $mount_point"
                  else
                    echo "Warning: PVC $pvc_name mount point is empty, using fallback"
                    ln -sf "$fallback_point" "$mount_point" 2>/dev/null || true
                  fi
                else
                  echo "PVC $pvc_name not available, using fallback storage"
                  ln -sf "$fallback_point" "$mount_point" 2>/dev/null || true
                fi
              }
              
              # Initial setup
              for pvc_type in buffer config db; do
                mount_pvc_if_available "$pvc_type"
              done
              
              # Monitor and maintain mounts
              while true; do
                sleep 60
                for pvc_type in buffer config db; do
                  mount_pvc_if_available "$pvc_type"
                done
              done
          
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL
          
          resources:
            limits:
              cpu: 50m
              memory: 64Mi
            requests:
              cpu: 10m
              memory: 32Mi
          
          volumeMounts:
            - name: podinfo
              mountPath: /etc/podinfo
              readOnly: true
            - name: fluent-bit-storage
              mountPath: /fluent-bit
            - name: shared-storage
              mountPath: /shared-storage
            - name: tmp
              mountPath: /tmp
        
        # Main Fluent Bit container
        - name: fluent-bit
          image: fluent/fluent-bit:2.2.0
          imagePullPolicy: IfNotPresent
          
          # Security context for the container
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          
          # Resource limits and requests
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          
          # Environment variables
          env:
            - name: FLUENT_CONF
              value: /fluent-bit/etc/fluent-bit.conf
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CLUSTER_NAME
              value: "aks-cluster"  # Replace with your actual cluster name
          
          # Ports
          ports:
            - name: http
              containerPort: 2020
              protocol: TCP
          
          # Health checks
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /api/v1/health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          
          # Volume mounts
          volumeMounts:
            # Configuration
            - name: config
              mountPath: /fluent-bit/etc
              readOnly: true
            
            # Persistent storage (managed by storage-manager sidecar)
            - name: fluent-bit-storage
              mountPath: /fluent-bit
            
            # Host log directories (read-only)
            - name: varlog
              mountPath: /var/log
              readOnly: true
            - name: varlibdockercontainers
              mountPath: /var/lib/docker/containers
              readOnly: true
            
            # Kubernetes service account token
            - name: kube-api-access
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true
            
            # Temporary directories
            - name: tmp
              mountPath: /tmp
            - name: var-tmp
              mountPath: /var/tmp
      
      # Volumes
      volumes:
        # ConfigMap volume
        - name: config
          configMap:
            name: fluent-bit-config
        
        # Pod info for getting node name
        - name: podinfo
          downwardAPI:
            items:
              - path: "nodename"
                fieldRef:
                  fieldPath: spec.nodeName
        
        # Shared storage for fallback
        - name: shared-storage
          emptyDir:
            sizeLimit: 15Gi
        
        # Main storage volume (emptyDir with size limit as fallback)
        - name: fluent-bit-storage
          emptyDir:
            sizeLimit: 15Gi
        
        # Host volumes (read-only)
        - name: varlog
          hostPath:
            path: /var/log
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
        
        # Service account token
        - name: kube-api-access
          projected:
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  items:
                    - key: ca.crt
                      path: ca.crt
                  name: kube-root-ca.crt
              - downwardAPI:
                  items:
                    - fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
                      path: namespace
        
        # Temporary volumes
        - name: tmp
          emptyDir: {}
        - name: var-tmp
          emptyDir: {}
  
  # Update strategy
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
