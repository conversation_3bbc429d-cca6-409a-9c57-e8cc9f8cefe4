---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit
    app.kubernetes.io/component: logging
    app.kubernetes.io/version: "2.2.0"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/component: logging
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "2020"
        prometheus.io/path: /api/v1/metrics/prometheus
    spec:
      serviceAccountName: fluent-bit
      hostNetwork: false
      dnsPolicy: ClusterFirst
      
      # Security context for the pod
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
        seccompProfile:
          type: RuntimeDefault
      
      # Node selection and tolerations
      nodeSelector:
        kubernetes.io/os: linux
      
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
          effect: NoSchedule
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
          effect: NoSchedule
        - operator: "Exists"
          effect: "NoExecute"
        - operator: "Exists"
          effect: "NoSchedule"
      
      # Init containers
      initContainers:
        # Wait for node-specific PVCs to be created
        - name: wait-for-pvcs
          image: bitnami/kubectl:1.28
          command:
            - /bin/bash
            - -c
            - |
              set -e
              echo "Waiting for node-specific PVCs to be created..."

              NODE_NAME=$(cat /etc/podinfo/nodename)
              echo "Node name: $NODE_NAME"

              # Wait for all required PVCs to exist and be bound
              for pvc_type in buffer config db; do
                pvc_name="fluent-bit-${pvc_type}-${NODE_NAME}"
                echo "Waiting for PVC: $pvc_name"

                # Wait for PVC to exist
                while ! kubectl get pvc "$pvc_name" -n fluent-bit >/dev/null 2>&1; do
                  echo "PVC $pvc_name not found, waiting..."
                  sleep 5
                done

                # Wait for PVC to be bound
                echo "Waiting for PVC $pvc_name to be bound..."
                kubectl wait --for=condition=Bound pvc/"$pvc_name" -n fluent-bit --timeout=300s
                echo "PVC $pvc_name is bound"
              done

              echo "All PVCs are ready!"

          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            capabilities:
              drop:
                - ALL

          volumeMounts:
            - name: podinfo
              mountPath: /etc/podinfo
              readOnly: true
            - name: tmp
              mountPath: /tmp

        # Set up permissions on mounted volumes
        - name: init-fluent-bit
          image: busybox:1.35
          command:
            - /bin/sh
            - -c
            - |
              mkdir -p /fluent-bit/db /fluent-bit/buffer /fluent-bit/config-backup
              chown -R 1000:1000 /fluent-bit
              chmod -R 755 /fluent-bit
          securityContext:
            runAsUser: 0
            runAsGroup: 0
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
              add:
                - CHOWN
                - FOWNER
          volumeMounts:
            - name: fluent-bit-db
              mountPath: /fluent-bit/db
            - name: fluent-bit-buffer
              mountPath: /fluent-bit/buffer
            - name: fluent-bit-config-backup
              mountPath: /fluent-bit/config-backup
      
      containers:
        - name: fluent-bit
          image: fluent/fluent-bit:2.2.0
          imagePullPolicy: IfNotPresent
          
          # Security context for the container
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          
          # Resource limits and requests
          resources:
            limits:
              cpu: 500m
              memory: 512Mi
            requests:
              cpu: 100m
              memory: 128Mi
          
          # Environment variables
          env:
            - name: FLUENT_CONF
              value: /fluent-bit/etc/fluent-bit.conf
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: CLUSTER_NAME
              value: "aks-cluster"  # Replace with your actual cluster name
          
          # Ports
          ports:
            - name: http
              containerPort: 2020
              protocol: TCP
          
          # Health checks
          livenessProbe:
            httpGet:
              path: /
              port: http
            initialDelaySeconds: 30
            periodSeconds: 30
            timeoutSeconds: 5
            failureThreshold: 3
          
          readinessProbe:
            httpGet:
              path: /api/v1/health
              port: http
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          
          # Volume mounts
          volumeMounts:
            # Configuration
            - name: config
              mountPath: /fluent-bit/etc
              readOnly: true

            # Dynamic persistent storage volumes (node-specific)
            - name: fluent-bit-db
              mountPath: /fluent-bit/db
            - name: fluent-bit-buffer
              mountPath: /fluent-bit/buffer
            - name: fluent-bit-config-backup
              mountPath: /fluent-bit/config-backup

            # Host log directories (read-only)
            - name: varlog
              mountPath: /var/log
              readOnly: true
            - name: varlibdockercontainers
              mountPath: /var/lib/docker/containers
              readOnly: true

            # Kubernetes service account token
            - name: kube-api-access
              mountPath: /var/run/secrets/kubernetes.io/serviceaccount
              readOnly: true

            # Temporary directories
            - name: tmp
              mountPath: /tmp
            - name: var-tmp
              mountPath: /var/tmp
      
      # Volumes
      volumes:
        # ConfigMap volume
        - name: config
          configMap:
            name: fluent-bit-config

        # Dynamic persistent volumes (node-specific PVCs)
        - name: fluent-bit-db
          persistentVolumeClaim:
            claimName: fluent-bit-db-$(NODE_NAME)
        - name: fluent-bit-buffer
          persistentVolumeClaim:
            claimName: fluent-bit-buffer-$(NODE_NAME)
        - name: fluent-bit-config-backup
          persistentVolumeClaim:
            claimName: fluent-bit-config-$(NODE_NAME)
        
        # Host volumes (read-only)
        - name: varlog
          hostPath:
            path: /var/log
        - name: varlibdockercontainers
          hostPath:
            path: /var/lib/docker/containers
        
        # Service account token
        - name: kube-api-access
          projected:
            sources:
              - serviceAccountToken:
                  expirationSeconds: 3607
                  path: token
              - configMap:
                  items:
                    - key: ca.crt
                      path: ca.crt
                  name: kube-root-ca.crt
              - downwardAPI:
                  items:
                    - fieldRef:
                        apiVersion: v1
                        fieldPath: metadata.namespace
                      path: namespace
        
        # Temporary volumes
        - name: tmp
          emptyDir: {}
        - name: var-tmp
          emptyDir: {}
  
  # Update strategy
  updateStrategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
