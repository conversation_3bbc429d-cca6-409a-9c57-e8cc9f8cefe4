---
# ServiceAccount for PVC Provisioner
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit-pvc-provisioner
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-provisioner
    app.kubernetes.io/component: provisioner

---
# ClusterRole for PVC Provisioner
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit-pvc-provisioner
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-provisioner
    app.kubernetes.io/component: provisioner
rules:
  # Node management
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "watch"]
  
  # PVC management
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  # PV management for cleanup
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch", "delete"]
  
  # Events for logging
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
  
  # ConfigMaps for state management
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch", "create", "update", "patch"]

---
# ClusterRoleBinding for PVC Provisioner
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit-pvc-provisioner
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-provisioner
    app.kubernetes.io/component: provisioner
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit-pvc-provisioner
subjects:
  - kind: ServiceAccount
    name: fluent-bit-pvc-provisioner
    namespace: fluent-bit

---
# ConfigMap for PVC Templates
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-pvc-templates
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-provisioner
    app.kubernetes.io/component: provisioner
data:
  buffer-pvc-template.yaml: |
    apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: fluent-bit-buffer-NODE_NAME
      namespace: fluent-bit
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/component: logging
        fluent-bit.io/storage-type: buffer
        fluent-bit.io/node: NODE_NAME
      annotations:
        fluent-bit.io/provisioned-by: fluent-bit-pvc-provisioner
        fluent-bit.io/node-affinity: NODE_NAME
    spec:
      accessModes:
        - ReadWriteOnce
      storageClassName: fluent-bit-node-storage
      resources:
        requests:
          storage: 10Gi
      selector:
        matchLabels:
          fluent-bit.io/node: NODE_NAME
          fluent-bit.io/storage-type: buffer

  config-pvc-template.yaml: |
    apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: fluent-bit-config-NODE_NAME
      namespace: fluent-bit
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/component: logging
        fluent-bit.io/storage-type: config
        fluent-bit.io/node: NODE_NAME
      annotations:
        fluent-bit.io/provisioned-by: fluent-bit-pvc-provisioner
        fluent-bit.io/node-affinity: NODE_NAME
    spec:
      accessModes:
        - ReadWriteOnce
      storageClassName: fluent-bit-node-storage
      resources:
        requests:
          storage: 1Gi
      selector:
        matchLabels:
          fluent-bit.io/node: NODE_NAME
          fluent-bit.io/storage-type: config

  db-pvc-template.yaml: |
    apiVersion: v1
    kind: PersistentVolumeClaim
    metadata:
      name: fluent-bit-db-NODE_NAME
      namespace: fluent-bit
      labels:
        app.kubernetes.io/name: fluent-bit
        app.kubernetes.io/component: logging
        fluent-bit.io/storage-type: database
        fluent-bit.io/node: NODE_NAME
      annotations:
        fluent-bit.io/provisioned-by: fluent-bit-pvc-provisioner
        fluent-bit.io/node-affinity: NODE_NAME
    spec:
      accessModes:
        - ReadWriteOnce
      storageClassName: fluent-bit-node-storage
      resources:
        requests:
          storage: 2Gi
      selector:
        matchLabels:
          fluent-bit.io/node: NODE_NAME
          fluent-bit.io/storage-type: database

---
# Deployment for PVC Provisioner Controller
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fluent-bit-pvc-provisioner
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-provisioner
    app.kubernetes.io/component: provisioner
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit-pvc-provisioner
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluent-bit-pvc-provisioner
        app.kubernetes.io/component: provisioner
    spec:
      serviceAccountName: fluent-bit-pvc-provisioner
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: provisioner
          image: bitnami/kubectl:1.28
          command:
            - /bin/bash
            - -c
            - |
              set -e
              
              echo "Starting Fluent Bit PVC Provisioner..."
              
              # Function to create PVCs for a node
              create_node_pvcs() {
                local node_name=$1
                echo "Creating PVCs for node: $node_name"
                
                # Create each PVC type
                for pvc_type in buffer config db; do
                  template_file="/templates/${pvc_type}-pvc-template.yaml"
                  if [[ -f "$template_file" ]]; then
                    # Replace NODE_NAME placeholder and apply
                    sed "s/NODE_NAME/$node_name/g" "$template_file" | kubectl apply -f -
                    echo "Created $pvc_type PVC for node $node_name"
                  fi
                done
              }
              
              # Function to cleanup PVCs for a node
              cleanup_node_pvcs() {
                local node_name=$1
                echo "Cleaning up PVCs for node: $node_name"
                
                # Delete PVCs with node label
                kubectl delete pvc -n fluent-bit -l "fluent-bit.io/node=$node_name" --ignore-not-found=true
                echo "Cleaned up PVCs for node $node_name"
              }
              
              # Function to get current nodes
              get_current_nodes() {
                kubectl get nodes -o jsonpath='{.items[*].metadata.name}'
              }
              
              # Function to get nodes with existing PVCs
              get_nodes_with_pvcs() {
                kubectl get pvc -n fluent-bit -l "fluent-bit.io/provisioned-by=fluent-bit-pvc-provisioner" \
                  -o jsonpath='{.items[*].metadata.labels.fluent-bit\.io/node}' | tr ' ' '\n' | sort -u
              }
              
              # Main reconciliation loop
              while true; do
                echo "Starting reconciliation cycle..."
                
                # Get current state
                current_nodes=$(get_current_nodes)
                nodes_with_pvcs=$(get_nodes_with_pvcs)
                
                # Create PVCs for new nodes
                for node in $current_nodes; do
                  if ! echo "$nodes_with_pvcs" | grep -q "^$node$"; then
                    echo "New node detected: $node"
                    create_node_pvcs "$node"
                  fi
                done
                
                # Cleanup PVCs for removed nodes
                for node in $nodes_with_pvcs; do
                  if ! echo "$current_nodes" | grep -q "$node"; then
                    echo "Removed node detected: $node"
                    cleanup_node_pvcs "$node"
                  fi
                done
                
                echo "Reconciliation cycle completed. Sleeping for 30 seconds..."
                sleep 30
              done
          
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          
          resources:
            limits:
              cpu: 100m
              memory: 128Mi
            requests:
              cpu: 50m
              memory: 64Mi
          
          volumeMounts:
            - name: templates
              mountPath: /templates
              readOnly: true
            - name: tmp
              mountPath: /tmp
          
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
      
      volumes:
        - name: templates
          configMap:
            name: fluent-bit-pvc-templates
        - name: tmp
          emptyDir: {}
