---
# Mutating Admission Webhook for Dynamic PVC Names
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingAdmissionWebhook
metadata:
  name: fluent-bit-pvc-mutator
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-mutator
    app.kubernetes.io/component: webhook
admissionReviewVersions: ["v1", "v1beta1"]
clientConfig:
  service:
    name: fluent-bit-pvc-webhook
    namespace: fluent-bit
    path: "/mutate"
rules:
  - operations: ["CREATE", "UPDATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
    namespaceSelector:
      matchLabels:
        name: fluent-bit
    objectSelector:
      matchLabels:
        app.kubernetes.io/name: fluent-bit
failurePolicy: Fail
sideEffects: None

---
# Service for the webhook
apiVersion: v1
kind: Service
metadata:
  name: fluent-bit-pvc-webhook
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-webhook
    app.kubernetes.io/component: webhook
spec:
  selector:
    app.kubernetes.io/name: fluent-bit-pvc-webhook
  ports:
    - name: webhook
      port: 443
      targetPort: 8443
      protocol: TCP

---
# Deployment for the webhook
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fluent-bit-pvc-webhook
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-pvc-webhook
    app.kubernetes.io/component: webhook
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit-pvc-webhook
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluent-bit-pvc-webhook
        app.kubernetes.io/component: webhook
    spec:
      serviceAccountName: fluent-bit-pvc-provisioner
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: webhook
          image: golang:1.21-alpine
          command:
            - /bin/sh
            - -c
            - |
              cat > /tmp/webhook.go << 'EOF'
              package main
              
              import (
                  "context"
                  "crypto/tls"
                  "encoding/json"
                  "fmt"
                  "net/http"
                  "strings"
                  
                  admissionv1 "k8s.io/api/admission/v1"
                  corev1 "k8s.io/api/core/v1"
                  metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
                  "k8s.io/apimachinery/pkg/runtime"
              )
              
              func main() {
                  certPath := "/etc/certs/tls.crt"
                  keyPath := "/etc/certs/tls.key"
                  
                  cert, err := tls.LoadX509KeyPair(certPath, keyPath)
                  if err != nil {
                      panic(err)
                  }
                  
                  server := &http.Server{
                      Addr:      ":8443",
                      TLSConfig: &tls.Config{Certificates: []tls.Certificate{cert}},
                  }
                  
                  mux := http.NewServeMux()
                  mux.HandleFunc("/mutate", handleMutate)
                  server.Handler = mux
                  
                  fmt.Println("Webhook server starting on :8443")
                  if err := server.ListenAndServeTLS("", ""); err != nil {
                      panic(err)
                  }
              }
              
              func handleMutate(w http.ResponseWriter, r *http.Request) {
                  var body []byte
                  if r.Body != nil {
                      if data, err := io.ReadAll(r.Body); err == nil {
                          body = data
                      }
                  }
                  
                  var admissionResponse *admissionv1.AdmissionResponse
                  ar := admissionv1.AdmissionReview{}
                  if err := json.Unmarshal(body, &ar); err != nil {
                      admissionResponse = &admissionv1.AdmissionResponse{
                          Result: &metav1.Status{
                              Message: err.Error(),
                          },
                      }
                  } else {
                      admissionResponse = mutate(&ar)
                  }
                  
                  admissionReview := admissionv1.AdmissionReview{}
                  if admissionResponse != nil {
                      admissionReview.Response = admissionResponse
                      if ar.Request != nil {
                          admissionReview.Response.UID = ar.Request.UID
                      }
                  }
                  
                  respBytes, _ := json.Marshal(admissionReview)
                  w.Header().Set("Content-Type", "application/json")
                  w.Write(respBytes)
              }
              
              func mutate(ar *admissionv1.AdmissionReview) *admissionv1.AdmissionResponse {
                  req := ar.Request
                  var pod corev1.Pod
                  if err := json.Unmarshal(req.Object.Raw, &pod); err != nil {
                      return &admissionv1.AdmissionResponse{
                          Result: &metav1.Status{
                              Message: err.Error(),
                          },
                      }
                  }
                  
                  // Only mutate fluent-bit pods
                  if pod.Labels["app.kubernetes.io/name"] != "fluent-bit" {
                      return &admissionv1.AdmissionResponse{Allowed: true}
                  }
                  
                  var patches []map[string]interface{}
                  nodeName := pod.Spec.NodeName
                  
                  // Update PVC names in volumes
                  for i, volume := range pod.Spec.Volumes {
                      if volume.PersistentVolumeClaim != nil {
                          claimName := volume.PersistentVolumeClaim.ClaimName
                          if strings.Contains(claimName, "placeholder") {
                              newClaimName := strings.Replace(claimName, "placeholder", nodeName, 1)
                              patch := map[string]interface{}{
                                  "op":    "replace",
                                  "path":  fmt.Sprintf("/spec/volumes/%d/persistentVolumeClaim/claimName", i),
                                  "value": newClaimName,
                              }
                              patches = append(patches, patch)
                          }
                      }
                  }
                  
                  patchBytes, _ := json.Marshal(patches)
                  return &admissionv1.AdmissionResponse{
                      Allowed: true,
                      Patch:   patchBytes,
                      PatchType: func() *admissionv1.PatchType {
                          pt := admissionv1.PatchTypeJSONPatch
                          return &pt
                      }(),
                  }
              }
              EOF
              
              cd /tmp
              go mod init webhook
              go mod tidy
              go run webhook.go
          
          ports:
            - containerPort: 8443
              name: webhook
              protocol: TCP
          
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
          
          volumeMounts:
            - name: certs
              mountPath: /etc/certs
              readOnly: true
            - name: tmp
              mountPath: /tmp
      
      volumes:
        - name: certs
          secret:
            secretName: fluent-bit-webhook-certs
        - name: tmp
          emptyDir: {}

---
# Certificate generation job
apiVersion: batch/v1
kind: Job
metadata:
  name: fluent-bit-webhook-cert-gen
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-webhook-cert-gen
    app.kubernetes.io/component: cert-generator
spec:
  template:
    spec:
      serviceAccountName: fluent-bit-pvc-provisioner
      restartPolicy: OnFailure
      containers:
        - name: cert-gen
          image: alpine/openssl:latest
          command:
            - /bin/sh
            - -c
            - |
              # Generate CA private key
              openssl genrsa -out ca.key 2048
              
              # Generate CA certificate
              openssl req -new -x509 -days 365 -key ca.key -out ca.crt -subj "/CN=fluent-bit-webhook-ca"
              
              # Generate server private key
              openssl genrsa -out tls.key 2048
              
              # Generate server certificate signing request
              openssl req -new -key tls.key -out server.csr -subj "/CN=fluent-bit-pvc-webhook.fluent-bit.svc"
              
              # Generate server certificate
              openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out tls.crt -days 365 \
                -extensions v3_req -extfile <(echo "[v3_req]"; echo "subjectAltName=DNS:fluent-bit-pvc-webhook.fluent-bit.svc,DNS:fluent-bit-pvc-webhook.fluent-bit.svc.cluster.local")
              
              # Create secret
              kubectl create secret tls fluent-bit-webhook-certs \
                --cert=tls.crt \
                --key=tls.key \
                -n fluent-bit \
                --dry-run=client -o yaml | kubectl apply -f -
              
              # Update webhook with CA bundle
              CA_BUNDLE=$(cat ca.crt | base64 | tr -d '\n')
              kubectl patch mutatingadmissionwebhook fluent-bit-pvc-mutator \
                --type='json' \
                -p="[{'op': 'replace', 'path': '/webhooks/0/clientConfig/caBundle', 'value': '$CA_BUNDLE'}]"
          
          securityContext:
            runAsUser: 0
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
