---
# Enhanced ServiceAccount for Node Lifecycle Controller
apiVersion: v1
kind: ServiceAccount
metadata:
  name: fluent-bit-node-controller
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-node-controller
    app.kubernetes.io/component: controller

---
# Enhanced ClusterRole for Node Lifecycle Controller
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: fluent-bit-node-controller
  labels:
    app.kubernetes.io/name: fluent-bit-node-controller
    app.kubernetes.io/component: controller
rules:
  # Node management
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "list", "watch"]
  
  # PVC management
  - apiGroups: [""]
    resources: ["persistentvolumeclaims"]
    verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
  
  # PV management for cleanup
  - apiGroups: [""]
    resources: ["persistentvolumes"]
    verbs: ["get", "list", "watch", "delete"]
  
  # Pod management for DaemonSet coordination
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["get", "list", "watch", "delete"]
  
  # Events for logging
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
  
  # ConfigMaps for state management
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["get", "list", "watch", "create", "update", "patch"]
  
  # DaemonSet management
  - apiGroups: ["apps"]
    resources: ["daemonsets"]
    verbs: ["get", "list", "watch", "update", "patch"]

---
# ClusterRoleBinding for Node Lifecycle Controller
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: fluent-bit-node-controller
  labels:
    app.kubernetes.io/name: fluent-bit-node-controller
    app.kubernetes.io/component: controller
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: fluent-bit-node-controller
subjects:
  - kind: ServiceAccount
    name: fluent-bit-node-controller
    namespace: fluent-bit

---
# ConfigMap for tracking node state
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-node-state
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-node-controller
    app.kubernetes.io/component: controller
data:
  nodes.json: "{}"

---
# Deployment for Node Lifecycle Controller
apiVersion: apps/v1
kind: Deployment
metadata:
  name: fluent-bit-node-controller
  namespace: fluent-bit
  labels:
    app.kubernetes.io/name: fluent-bit-node-controller
    app.kubernetes.io/component: controller
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: fluent-bit-node-controller
  template:
    metadata:
      labels:
        app.kubernetes.io/name: fluent-bit-node-controller
        app.kubernetes.io/component: controller
    spec:
      serviceAccountName: fluent-bit-node-controller
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: controller
          image: bitnami/kubectl:1.28
          command:
            - /bin/bash
            - -c
            - |
              set -e
              
              echo "Starting Fluent Bit Node Lifecycle Controller..."
              
              NAMESPACE="fluent-bit"
              STATE_CONFIGMAP="fluent-bit-node-state"
              
              # Function to get current nodes
              get_current_nodes() {
                kubectl get nodes -o jsonpath='{.items[*].metadata.name}' | tr ' ' '\n' | sort
              }
              
              # Function to get tracked nodes from ConfigMap
              get_tracked_nodes() {
                kubectl get configmap "$STATE_CONFIGMAP" -n "$NAMESPACE" -o jsonpath='{.data.nodes\.json}' 2>/dev/null | \
                  jq -r 'keys[]' 2>/dev/null || echo ""
              }
              
              # Function to update tracked nodes
              update_tracked_nodes() {
                local nodes_json="{}"
                for node in $(get_current_nodes); do
                  nodes_json=$(echo "$nodes_json" | jq --arg node "$node" '. + {($node): {"created": now | todate}}')
                done
                
                kubectl patch configmap "$STATE_CONFIGMAP" -n "$NAMESPACE" \
                  --type merge -p "{\"data\":{\"nodes.json\":\"$(echo "$nodes_json" | sed 's/"/\\"/g')\"}}"
              }
              
              # Function to create PVCs for a node
              create_node_pvcs() {
                local node_name=$1
                echo "Creating PVCs for node: $node_name"
                
                # Create each PVC type
                for pvc_type in buffer config db; do
                  local pvc_name="fluent-bit-${pvc_type}-${node_name}"
                  
                  # Check if PVC already exists
                  if kubectl get pvc "$pvc_name" -n "$NAMESPACE" >/dev/null 2>&1; then
                    echo "PVC $pvc_name already exists"
                    continue
                  fi
                  
                  # Determine storage size
                  local storage_size
                  case $pvc_type in
                    "buffer") storage_size="10Gi" ;;
                    "config") storage_size="1Gi" ;;
                    "db") storage_size="2Gi" ;;
                    *) storage_size="1Gi" ;;
                  esac
                  
                  echo "Creating PVC: $pvc_name with size: $storage_size"
                  
                  # Create PVC
                  cat <<EOF | kubectl apply -f -
              apiVersion: v1
              kind: PersistentVolumeClaim
              metadata:
                name: $pvc_name
                namespace: $NAMESPACE
                labels:
                  app.kubernetes.io/name: fluent-bit
                  app.kubernetes.io/component: logging
                  fluent-bit.io/storage-type: $pvc_type
                  fluent-bit.io/node: $node_name
                annotations:
                  fluent-bit.io/provisioned-by: fluent-bit-node-controller
                  fluent-bit.io/node-affinity: $node_name
                  fluent-bit.io/created-at: "$(date -Iseconds)"
              spec:
                accessModes:
                  - ReadWriteOnce
                storageClassName: fluent-bit-node-storage
                resources:
                  requests:
                    storage: $storage_size
              EOF
                  
                  if [ $? -eq 0 ]; then
                    echo "Successfully created PVC: $pvc_name"
                    
                    # Create event
                    kubectl create event \
                      --type=Normal \
                      --reason=PVCCreated \
                      --message="Created PVC $pvc_name for node $node_name" \
                      --reporting-controller=fluent-bit-node-controller \
                      --reporting-instance=fluent-bit-node-controller \
                      --namespace="$NAMESPACE" || true
                  else
                    echo "Failed to create PVC: $pvc_name"
                  fi
                done
              }
              
              # Function to cleanup PVCs for a node
              cleanup_node_pvcs() {
                local node_name=$1
                echo "Cleaning up PVCs for removed node: $node_name"
                
                # Get all PVCs for this node
                local pvcs=$(kubectl get pvc -n "$NAMESPACE" -l "fluent-bit.io/node=$node_name" -o jsonpath='{.items[*].metadata.name}')
                
                if [ -n "$pvcs" ]; then
                  echo "Found PVCs to cleanup: $pvcs"
                  
                  # Delete each PVC
                  for pvc in $pvcs; do
                    echo "Deleting PVC: $pvc"
                    kubectl delete pvc "$pvc" -n "$NAMESPACE" --ignore-not-found=true
                    
                    if [ $? -eq 0 ]; then
                      echo "Successfully deleted PVC: $pvc"
                      
                      # Create event
                      kubectl create event \
                        --type=Normal \
                        --reason=PVCDeleted \
                        --message="Deleted PVC $pvc for removed node $node_name" \
                        --reporting-controller=fluent-bit-node-controller \
                        --reporting-instance=fluent-bit-node-controller \
                        --namespace="$NAMESPACE" || true
                    else
                      echo "Failed to delete PVC: $pvc"
                    fi
                  done
                else
                  echo "No PVCs found for node: $node_name"
                fi
              }
              
              # Function to restart DaemonSet pods on a node (to pick up new PVCs)
              restart_node_pods() {
                local node_name=$1
                echo "Restarting Fluent Bit pods on node: $node_name"
                
                # Get pods on this node
                local pods=$(kubectl get pods -n "$NAMESPACE" -l "app.kubernetes.io/name=fluent-bit" \
                  --field-selector="spec.nodeName=$node_name" -o jsonpath='{.items[*].metadata.name}')
                
                if [ -n "$pods" ]; then
                  for pod in $pods; do
                    echo "Deleting pod: $pod (will be recreated by DaemonSet)"
                    kubectl delete pod "$pod" -n "$NAMESPACE" --ignore-not-found=true
                  done
                fi
              }
              
              # Main reconciliation loop
              echo "Starting main reconciliation loop..."
              while true; do
                echo "$(date): Starting reconciliation cycle..."
                
                # Get current state
                current_nodes=$(get_current_nodes)
                tracked_nodes=$(get_tracked_nodes)
                
                echo "Current nodes: $(echo "$current_nodes" | tr '\n' ' ')"
                echo "Tracked nodes: $(echo "$tracked_nodes" | tr '\n' ' ')"
                
                # Handle new nodes
                for node in $current_nodes; do
                  if ! echo "$tracked_nodes" | grep -q "^$node$"; then
                    echo "New node detected: $node"
                    create_node_pvcs "$node"
                    
                    # Wait a bit for PVCs to be created and bound
                    sleep 10
                    
                    # Restart pods on this node to pick up new PVCs
                    restart_node_pods "$node"
                  fi
                done
                
                # Handle removed nodes
                for node in $tracked_nodes; do
                  if ! echo "$current_nodes" | grep -q "^$node$"; then
                    echo "Removed node detected: $node"
                    cleanup_node_pvcs "$node"
                  fi
                done
                
                # Update tracked nodes
                update_tracked_nodes
                
                echo "$(date): Reconciliation cycle completed. Sleeping for 30 seconds..."
                sleep 30
              done
          
          securityContext:
            allowPrivilegeEscalation: false
            readOnlyRootFilesystem: true
            runAsNonRoot: true
            runAsUser: 1000
            runAsGroup: 1000
            capabilities:
              drop:
                - ALL
          
          resources:
            limits:
              cpu: 200m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 128Mi
          
          volumeMounts:
            - name: tmp
              mountPath: /tmp
          
          env:
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
      
      volumes:
        - name: tmp
          emptyDir: {}
