# Fluent Bit DaemonSet for Azure Kubernetes Service (AKS) with Dynamic PVCs

This repository contains Kubernetes manifests and deployment scripts for running Fluent Bit as a DaemonSet on Azure Kubernetes Service (AKS) with **dynamic per-node persistent storage** using Persistent Volume Claims (PVC).

## Overview

This deployment provides:
- **Fluent Bit DaemonSet** running on all nodes in your AKS cluster
- **Dynamic per-node persistent storage** that automatically scales with your cluster
- **Automatic PVC provisioning** when nodes join the cluster
- **Automatic PVC cleanup** when nodes are removed during scale-down
- **Data locality** ensuring each Fluent Bit pod uses storage on its respective node
- **RBAC permissions** with least-privilege access
- **Security hardening** with non-root containers and read-only filesystems
- **Resource limits** and health checks
- **Azure-optimized storage classes** for Premium SSD performance
- **Fallback storage** using emptyDir when PVCs are not available

## Architecture

### Dynamic Per-Node PVC Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                              AKS Cluster                                   │
├─────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │
│  │     Node 1      │  │     Node 2      │  │     Node N      │            │
│  │                 │  │                 │  │                 │            │
│  │ ┌─────────────┐ │  │ ┌─────────────┐ │  │ ┌─────────────┐ │            │
│  │ │Fluent Bit   │ │  │ │Fluent Bit   │ │  │ │Fluent Bit   │ │            │
│  │ │Pod + Sidecar│ │  │ │Pod + Sidecar│ │  │ │Pod + Sidecar│ │            │
│  │ └─────────────┘ │  │ └─────────────┘ │  │ └─────────────┘ │            │
│  │       │         │  │       │         │  │       │         │            │
│  │ ┌─────▼─────┐   │  │ ┌─────▼─────┐   │  │ ┌─────▼─────┐   │            │
│  │ │Node-Local │   │  │ │Node-Local │   │  │ │Node-Local │   │            │
│  │ │PVCs (3x)  │   │  │ │PVCs (3x)  │   │  │ │PVCs (3x)  │   │            │
│  │ └───────────┘   │  │ └───────────┘   │  │ └───────────┘   │            │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘            │
├─────────────────────────────────────────────────────────────────────────────┤
│                          Control Plane                                     │
│  ┌─────────────────┐  ┌─────────────────────────────────────────────────┐  │
│  │PVC Provisioner  │  │         Node Lifecycle Controller               │  │
│  │Controller       │  │                                                 │  │
│  │                 │  │ • Watches for node join/leave events           │  │
│  │ • Creates PVCs  │  │ • Creates PVCs: fluent-bit-{type}-{node-name}  │  │
│  │ • Manages       │  │ • Cleans up PVCs when nodes are removed        │  │
│  │   templates     │  │ • Ensures data locality                        │  │
│  └─────────────────┘  └─────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────┘

Per Node PVCs:
• fluent-bit-buffer-{node-name} (10Gi) - Log buffering
• fluent-bit-config-{node-name} (1Gi)  - Configuration backup
• fluent-bit-db-{node-name} (2Gi)      - Internal database
```

## Files Description

| File | Description |
|------|-------------|
| `01-namespace-rbac.yaml` | Namespace, ServiceAccount, ClusterRole, and ClusterRoleBinding |
| `02-storage.yaml` | StorageClass configurations for dynamic PVC provisioning |
| `03-configmap.yaml` | Fluent Bit configuration with persistent storage settings |
| `04-daemonset-final.yaml` | DaemonSet with dynamic PVC support and storage manager sidecar |
| `05-pvc-provisioner.yaml` | PVC provisioner controller with templates |
| `07-node-lifecycle-controller.yaml` | Node lifecycle controller for automatic PVC management |
| `deploy.sh` | Enhanced deployment script with dynamic PVC support |
| `README.md` | This documentation file |

### Legacy Files (for reference)
| File | Description |
|------|-------------|
| `04-daemonset.yaml` | Original static PVC DaemonSet |
| `04-daemonset-dynamic.yaml` | Intermediate dynamic PVC approach |
| `06-pvc-webhook.yaml` | Webhook-based approach (complex, not recommended) |

## Prerequisites

1. **Azure Kubernetes Service (AKS) cluster** with at least one node
2. **kubectl** configured to connect to your AKS cluster
3. **Cluster admin permissions** to create ClusterRole and ClusterRoleBinding
4. **Azure Premium SSD storage** available in your cluster

### Verify Prerequisites

```bash
# Check kubectl connection
kubectl cluster-info

# Check available storage classes
kubectl get storageclass

# Check node readiness
kubectl get nodes
```

## Quick Start

1. **Clone or download** the manifests to your local machine

2. **Make the deployment script executable:**
   ```bash
   chmod +x deploy.sh
   ```

3. **Deploy Fluent Bit:**
   ```bash
   ./deploy.sh deploy
   ```

4. **Check deployment status:**
   ```bash
   ./deploy.sh status
   ```

5. **Check dynamic PVC details:**
   ```bash
   ./deploy.sh pvcs
   ```

## Manual Deployment

If you prefer to deploy manually:

```bash
# Apply manifests in order
kubectl apply -f 01-namespace-rbac.yaml
kubectl apply -f 02-storage.yaml
kubectl apply -f 03-configmap.yaml
kubectl apply -f 05-pvc-provisioner.yaml
kubectl apply -f 07-node-lifecycle-controller.yaml
kubectl apply -f 04-daemonset-final.yaml

# Wait for controllers to be ready
kubectl wait --for=condition=Available deployment/fluent-bit-node-controller -n fluent-bit --timeout=300s

# Wait for deployment
kubectl rollout status daemonset/fluent-bit -n fluent-bit

# Check status
kubectl get pods -n fluent-bit -o wide

# Check dynamic PVCs
kubectl get pvc -n fluent-bit -l "fluent-bit.io/node"
```

## Configuration

### Dynamic PVC Features

The deployment automatically creates **three PVCs per node**:

- **Buffer PVC** (10Gi): `fluent-bit-buffer-{node-name}` - For log buffering and temporary storage
- **Config PVC** (1Gi): `fluent-bit-config-{node-name}` - For configuration backups
- **Database PVC** (2Gi): `fluent-bit-db-{node-name}` - For Fluent Bit's internal database

#### How Dynamic PVCs Work

1. **Node Lifecycle Controller** monitors cluster nodes
2. When a **new node joins**, controller creates 3 PVCs for that node
3. **DaemonSet pods** wait for PVCs to be bound before starting
4. **Storage Manager sidecar** ensures proper PVC mounting
5. When a **node is removed**, controller cleans up associated PVCs
6. **Fallback mechanism** uses emptyDir if PVCs are unavailable

#### Benefits

- **Automatic scaling**: PVCs scale with your cluster
- **Data locality**: Each pod uses storage on its own node
- **Resource efficiency**: No unused PVCs for non-existent nodes
- **Clean operations**: Automatic cleanup prevents storage waste
- **High availability**: Fallback storage ensures pods always start

### Cluster Name

Update the cluster name in the DaemonSet:

```yaml
env:
  - name: CLUSTER_NAME
    value: "your-aks-cluster-name"  # Update this
```

### Output Configuration

The default configuration outputs to stdout. To configure Azure Log Analytics:

1. Uncomment the Azure output section in `03-configmap.yaml`
2. Set your workspace ID and shared key as environment variables or secrets

## Monitoring and Troubleshooting

### Check Pod Status

```bash
kubectl get pods -n fluent-bit -o wide
```

### View Logs

```bash
# View logs from all pods
kubectl logs -n fluent-bit -l app.kubernetes.io/name=fluent-bit

# View logs from specific pod
kubectl logs -n fluent-bit <pod-name>

# Follow logs
kubectl logs -n fluent-bit -l app.kubernetes.io/name=fluent-bit -f
```

### Check Dynamic PVCs

```bash
# Check all PVCs
kubectl get pvc -n fluent-bit

# Check PVCs by node
kubectl get pvc -n fluent-bit -l "fluent-bit.io/node" -o custom-columns="NAME:.metadata.name,NODE:.metadata.labels.fluent-bit\.io/node,TYPE:.metadata.labels.fluent-bit\.io/storage-type,STATUS:.status.phase"

# Check PV details
kubectl get pv

# Use deployment script for detailed view
./deploy.sh pvcs
```

### Health Checks

Fluent Bit exposes health endpoints:

```bash
# Port forward to access health endpoint
kubectl port-forward -n fluent-bit <pod-name> 2020:2020

# Check health (in another terminal)
curl http://localhost:2020/api/v1/health
```

## Security Features

- **Non-root containers** with user ID 1000
- **Read-only root filesystem** for enhanced security
- **Dropped capabilities** with minimal required permissions
- **Security contexts** applied at pod and container level
- **Network policies** ready (can be added as needed)

## Resource Management

Default resource limits:
- **CPU**: 500m limit, 100m request
- **Memory**: 512Mi limit, 128Mi request

Adjust based on your cluster's log volume and performance requirements.

## Cleanup

To remove the Fluent Bit deployment:

```bash
./deploy.sh cleanup
```

Or manually:

```bash
# Remove DaemonSet and controllers
kubectl delete -f 04-daemonset-final.yaml
kubectl delete -f 07-node-lifecycle-controller.yaml
kubectl delete -f 05-pvc-provisioner.yaml

# Clean up dynamic PVCs
kubectl delete pvc -n fluent-bit -l "fluent-bit.io/provisioned-by"

# Remove remaining components
kubectl delete -f 03-configmap.yaml
kubectl delete -f 02-storage.yaml
kubectl delete -f 01-namespace-rbac.yaml
```

## Customization

### Adding Custom Parsers

Edit `03-configmap.yaml` and add your parsers to the `parsers.conf` section.

### Modifying Storage Size

Edit `02-storage.yaml` and adjust the storage requests in the PVC specifications.

### Adding Outputs

Edit `03-configmap.yaml` and add additional output configurations as needed.

## Support

For issues related to:
- **Fluent Bit configuration**: Check the [official documentation](https://docs.fluentbit.io/)
- **AKS-specific issues**: Consult [Azure AKS documentation](https://docs.microsoft.com/en-us/azure/aks/)
- **Kubernetes concepts**: Refer to [Kubernetes documentation](https://kubernetes.io/docs/)

## License

This configuration is provided as-is for educational and production use. Please review and test thoroughly before deploying to production environments.
