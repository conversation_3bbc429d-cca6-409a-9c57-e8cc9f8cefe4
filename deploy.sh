#!/bin/bash

# Fluent Bit DaemonSet Deployment Script for AKS
# This script deploys Fluent Bit with persistent storage on Azure Kubernetes Service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="fluent-bit"
CLUSTER_NAME="${CLUSTER_NAME:-aks-cluster}"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if kubectl is installed
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl is not installed. Please install kubectl first."
        exit 1
    fi
    
    # Check if connected to a cluster
    if ! kubectl cluster-info &> /dev/null; then
        log_error "Not connected to a Kubernetes cluster. Please configure kubectl."
        exit 1
    fi
    
    # Check if this is an AKS cluster
    CLUSTER_INFO=$(kubectl cluster-info)
    if [[ ! $CLUSTER_INFO == *"azmk8s.io"* ]]; then
        log_warning "This doesn't appear to be an AKS cluster. Continuing anyway..."
    fi
    
    log_success "Prerequisites check passed"
}

deploy_manifests() {
    log_info "Deploying Fluent Bit manifests with dynamic PVC provisioning..."

    # Apply manifests in order
    local manifests=(
        "01-namespace-rbac.yaml"
        "02-storage.yaml"
        "03-configmap.yaml"
        "05-pvc-provisioner.yaml"
        "07-node-lifecycle-controller.yaml"
        "04-daemonset-final.yaml"
    )

    for manifest in "${manifests[@]}"; do
        if [[ -f "$manifest" ]]; then
            log_info "Applying $manifest..."
            kubectl apply -f "$manifest"

            # Add delays for certain components
            case "$manifest" in
                "05-pvc-provisioner.yaml")
                    log_info "Waiting for PVC provisioner to start..."
                    sleep 10
                    ;;
                "07-node-lifecycle-controller.yaml")
                    log_info "Waiting for node lifecycle controller to initialize..."
                    sleep 15
                    ;;
            esac
        else
            log_error "Manifest file $manifest not found!"
            exit 1
        fi
    done

    log_success "All manifests applied successfully"
}

wait_for_deployment() {
    log_info "Waiting for Fluent Bit deployment with dynamic PVCs to be ready..."

    # Wait for namespace to be active
    kubectl wait --for=condition=Active namespace/$NAMESPACE --timeout=60s

    # Wait for controllers to be ready
    log_info "Waiting for PVC provisioner to be ready..."
    kubectl wait --for=condition=Available deployment/fluent-bit-pvc-provisioner -n $NAMESPACE --timeout=300s

    log_info "Waiting for node lifecycle controller to be ready..."
    kubectl wait --for=condition=Available deployment/fluent-bit-node-controller -n $NAMESPACE --timeout=300s

    # Wait for dynamic PVCs to be created (check for at least one node's PVCs)
    log_info "Waiting for dynamic PVCs to be created..."
    timeout=300
    elapsed=0
    while [ $elapsed -lt $timeout ]; do
        pvc_count=$(kubectl get pvc -n $NAMESPACE -l "fluent-bit.io/provisioned-by" --no-headers 2>/dev/null | wc -l)
        if [ "$pvc_count" -gt 0 ]; then
            log_info "Found $pvc_count dynamic PVCs"
            break
        fi
        echo "Waiting for dynamic PVCs to be created... (${elapsed}s/${timeout}s)"
        sleep 10
        elapsed=$((elapsed + 10))
    done

    # Wait for DaemonSet to be ready
    log_info "Waiting for DaemonSet to be ready..."
    kubectl rollout status daemonset/fluent-bit -n $NAMESPACE --timeout=600s

    log_success "Fluent Bit DaemonSet with dynamic PVCs is ready!"
}

show_status() {
    log_info "Deployment Status:"
    echo

    # Show namespace
    echo "Namespace:"
    kubectl get namespace $NAMESPACE
    echo

    # Show controllers
    echo "Controllers:"
    kubectl get deployment -n $NAMESPACE
    echo

    # Show dynamic PVCs
    echo "Dynamic Persistent Volume Claims:"
    kubectl get pvc -n $NAMESPACE -l "fluent-bit.io/provisioned-by" 2>/dev/null || echo "No dynamic PVCs found"
    echo

    # Show all PVCs
    echo "All Persistent Volume Claims:"
    kubectl get pvc -n $NAMESPACE
    echo

    # Show DaemonSet
    echo "DaemonSet:"
    kubectl get daemonset -n $NAMESPACE
    echo

    # Show pods
    echo "Pods:"
    kubectl get pods -n $NAMESPACE -o wide
    echo

    # Show PVC distribution per node
    echo "PVC Distribution per Node:"
    kubectl get pvc -n $NAMESPACE -l "fluent-bit.io/node" -o custom-columns="NAME:.metadata.name,NODE:.metadata.labels.fluent-bit\.io/node,TYPE:.metadata.labels.fluent-bit\.io/storage-type,STATUS:.status.phase" 2>/dev/null || echo "No node-specific PVCs found"
    echo

    # Show services
    echo "Services:"
    kubectl get svc -n $NAMESPACE 2>/dev/null || echo "No services found"
}

show_logs() {
    log_info "Recent Fluent Bit logs:"
    echo
    
    # Get a pod name
    POD_NAME=$(kubectl get pods -n $NAMESPACE -l app.kubernetes.io/name=fluent-bit -o jsonpath='{.items[0].metadata.name}' 2>/dev/null)
    
    if [[ -n "$POD_NAME" ]]; then
        kubectl logs -n $NAMESPACE "$POD_NAME" --tail=20
    else
        log_warning "No Fluent Bit pods found"
    fi
}

cleanup() {
    log_warning "Cleaning up Fluent Bit deployment with dynamic PVCs..."

    # Delete in reverse order
    kubectl delete -f 04-daemonset-final.yaml --ignore-not-found=true
    kubectl delete -f 07-node-lifecycle-controller.yaml --ignore-not-found=true
    kubectl delete -f 05-pvc-provisioner.yaml --ignore-not-found=true

    # Clean up dynamic PVCs
    log_info "Cleaning up dynamic PVCs..."
    kubectl delete pvc -n $NAMESPACE -l "fluent-bit.io/provisioned-by" --ignore-not-found=true

    kubectl delete -f 03-configmap.yaml --ignore-not-found=true
    kubectl delete -f 02-storage.yaml --ignore-not-found=true
    kubectl delete -f 01-namespace-rbac.yaml --ignore-not-found=true

    log_success "Cleanup completed"
}

show_pvc_details() {
    log_info "Dynamic PVC Details:"
    echo

    # Show PVCs grouped by node
    nodes=$(kubectl get nodes -o jsonpath='{.items[*].metadata.name}')
    for node in $nodes; do
        echo "Node: $node"
        kubectl get pvc -n $NAMESPACE -l "fluent-bit.io/node=$node" --no-headers 2>/dev/null | \
            awk '{printf "  %-30s %-10s %-10s\n", $1, $3, $2}' || echo "  No PVCs found"
        echo
    done
}

# Main execution
case "${1:-deploy}" in
    "deploy")
        log_info "Starting Fluent Bit deployment on AKS..."
        check_prerequisites
        deploy_manifests
        wait_for_deployment
        show_status
        show_logs
        log_success "Fluent Bit deployment completed successfully!"
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "pvcs")
        show_pvc_details
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [command]"
        echo
        echo "Commands:"
        echo "  deploy   - Deploy Fluent Bit with dynamic PVCs (default)"
        echo "  status   - Show deployment status"
        echo "  logs     - Show recent logs"
        echo "  pvcs     - Show detailed PVC information per node"
        echo "  cleanup  - Remove Fluent Bit deployment and dynamic PVCs"
        echo "  help     - Show this help message"
        echo
        echo "Dynamic PVC Features:"
        echo "  - Automatic PVC creation per node"
        echo "  - PVC cleanup when nodes are removed"
        echo "  - Node-local storage for better performance"
        echo "  - Fallback to emptyDir if PVCs are not available"
        ;;
    *)
        log_error "Unknown command: $1"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
